using ProjectTemplate.Models.Entities;
using ProjectTemplate.Shared.RequestFeatures;
using ProjectTemplate.Shared.DataTransferObjects;

namespace ProjectTemplate.Contracts.Repository;

public interface ILeaveRepository : IRepositoryBase<Leave>
{
    // Leave CRUD operations
    Task<PagedList<Leave>> GetAllLeavesAsync(LeaveRequestParameters parameters, bool trackChanges);
    Task<PagedList<Leave>> GetLeavesByEmployeeIdAsync(string employeeId, LeaveRequestParameters parameters, bool trackChanges);
    Task<PagedList<Leave>> GetPendingLeavesAsync(LeaveRequestParameters parameters, bool trackChanges);
    Task<Leave?> GetLeaveByIdAsync(Guid id, bool trackChanges);
    Task<IEnumerable<Leave>> GetLeavesByStatusAsync(LeaveStatus status, bool trackChanges);
    Task<IEnumerable<Leave>> GetLeavesByDateRangeAsync(DateTime startDate, DateTime endDate, bool trackChanges);
    Task<IEnumerable<Leave>> GetLeavesByTypeAsync(LeaveType type, LeaveRequestParameters parameters, bool trackChanges);
    Task<int> GetLeaveCountByEmployeeAndTypeAsync(string employeeId, LeaveType leaveType, int year, bool trackChanges);

    void CreateLeave(Leave leave);
    void UpdateLeave(Leave leave);
    void DeleteLeave(Leave leave);

    // Leave Balance operations
    Task<IEnumerable<LeaveBalance>> GetLeaveBalancesAsync(string? employeeId, int? year, bool trackChanges);
    Task<LeaveBalance?> GetLeaveBalanceAsync(string employeeId, LeaveType leaveType, int year, bool trackChanges);
    Task<IEnumerable<LeaveBalance>> GetLeaveBalancesByEmployeeAsync(string employeeId, int year, bool trackChanges);

    void CreateLeaveBalance(LeaveBalance leaveBalance);
    void UpdateLeaveBalance(LeaveBalance leaveBalance);
    void DeleteLeaveBalance(LeaveBalance leaveBalance);

    // Statistics and reporting
    Task<int> GetLeaveCountAsync();
    Task<int> GetLeaveCountByStatusAsync(LeaveStatus status);
    Task<Dictionary<LeaveStatus, int>> GetLeaveCountByStatusGroupAsync();
    Task<int> GetLeaveCountByTypeAsync(LeaveType type);
    Task<Dictionary<LeaveType, int>> GetLeaveCountByTypeGroupAsync();
    Task<int> GetLeaveCountByEmployeeAsync(string employeeId);
    Task<int> GetPendingLeaveCountAsync();
    Task<int> GetTotalLeaveDaysRequestedAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<int> GetTotalLeaveDaysApprovedAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<LeaveType?> GetMostRequestedLeaveTypeAsync(DateTime? startDate = null, DateTime? endDate = null);

    // Bulk operations
    Task<IEnumerable<Leave>> GetLeavesByIdsAsync(Guid[] ids, bool trackChanges);

    // Validation helpers
    Task<bool> HasOverlappingLeaveAsync(string employeeId, DateTime startDate, DateTime endDate, Guid? excludeLeaveId = null);
    Task<int> GetUsedLeaveDaysAsync(string employeeId, LeaveType leaveType, int year);
}