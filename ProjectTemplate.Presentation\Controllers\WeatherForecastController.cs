using Microsoft.AspNetCore.Mvc;
using ProjectTemplate.Service.Contracts;

namespace ProjectTemplate.Presentation.Controllers;

[ApiController]
[Route("api/[controller]")]
public class WeatherForecastController : BaseApiController
{
    private static readonly string[] Summaries = new[]
    {
        "Freezing", "Bracing", "<PERSON><PERSON>", "<PERSON>", "Mild", "Warm", "<PERSON><PERSON><PERSON>", "<PERSON>", "Sweltering", "Scorching"
    };

    public WeatherForecastController(IServiceManager serviceManager) : base(serviceManager)
    {
    }

    [HttpGet]
    public IEnumerable<WeatherForecast> Get()
    {
        return Enumerable.Range(1, 5).Select(index => new WeatherForecast
        {
            Date = DateOnly.FromDateTime(DateTime.Now.AddDays(index)),
            TemperatureC = Random.Shared.Next(-20, 55),
            Summary = Summaries[Random.Shared.Next(Summaries.Length)]
        })
        .ToArray();
    }
}

public class WeatherForecast
{
    public DateOnly Date { get; set; }
    public int TemperatureC { get; set; }
    public int TemperatureF => 32 + (int)(TemperatureC / 0.5556);
    public string? Summary { get; set; }
}
