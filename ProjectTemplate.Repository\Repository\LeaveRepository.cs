using Microsoft.EntityFrameworkCore;
using ProjectTemplate.Contracts.Repository;
using ProjectTemplate.Models.DataSource;
using ProjectTemplate.Models.Entities;
using ProjectTemplate.Shared.RequestFeatures;
using ProjectTemplate.Shared.DataTransferObjects;

namespace ProjectTemplate.Repository.Repository;

public sealed class LeaveRepository : RepositoryBase<Leave>, ILeaveRepository
{
    public LeaveRepository(ProjectTemplateContext repositoryContext) : base(repositoryContext)
    {
    }

    public async Task<PagedList<Leave>> GetAllLeavesAsync(LeaveRequestParameters parameters, bool trackChanges)
    {
        var query = FindAll(trackChanges)
            .Include(l => l.Employee)
            .Include(l => l.Approver)
            .Include(l => l.Rejector)
            .AsQueryable();

        // Apply filters
        query = ApplyFilters(query, parameters);

        // Apply sorting
        query = ApplySorting(query, parameters.SortBy, parameters.SortDirection);

        return await PagedList<Leave>.ToPagedListAsync(query, parameters.PageNumber, parameters.PageSize);
    }

    public async Task<PagedList<Leave>> GetLeavesByEmployeeIdAsync(string employeeId, LeaveRequestParameters parameters, bool trackChanges)
    {
        var query = FindByCondition(l => l.EmployeeId == employeeId, trackChanges)
            .Include(l => l.Employee)
            .Include(l => l.Approver)
            .Include(l => l.Rejector)
            .AsQueryable();

        // Apply filters
        query = ApplyFilters(query, parameters);

        // Apply sorting
        query = ApplySorting(query, parameters.SortBy, parameters.SortDirection);

        return await PagedList<Leave>.ToPagedListAsync(query, parameters.PageNumber, parameters.PageSize);
    }

    public async Task<PagedList<Leave>> GetPendingLeavesAsync(LeaveRequestParameters parameters, bool trackChanges)
    {
        var query = FindByCondition(l => l.Status == LeaveStatus.Pending, trackChanges)
            .Include(l => l.Employee)
            .Include(l => l.Approver)
            .Include(l => l.Rejector)
            .AsQueryable();

        // Apply filters
        query = ApplyFilters(query, parameters);

        // Apply sorting
        query = ApplySorting(query, parameters.SortBy, parameters.SortDirection);

        return await PagedList<Leave>.ToPagedListAsync(query, parameters.PageNumber, parameters.PageSize);
    }

    public async Task<Leave?> GetLeaveByIdAsync(Guid id, bool trackChanges)
    {
        return await FindByCondition(l => l.Id == id, trackChanges)
            .Include(l => l.Employee)
            .Include(l => l.Approver)
            .Include(l => l.Rejector)
            .FirstOrDefaultAsync();
    }

    public async Task<IEnumerable<Leave>> GetLeavesByStatusAsync(LeaveStatus status, bool trackChanges)
    {
        return await FindByCondition(l => l.Status == status, trackChanges)
            .Include(l => l.Employee)
            .ToListAsync();
    }

    public async Task<IEnumerable<Leave>> GetLeavesByDateRangeAsync(DateTime startDate, DateTime endDate, bool trackChanges)
    {
        return await FindByCondition(l => l.StartDate >= startDate && l.EndDate <= endDate, trackChanges)
            .Include(l => l.Employee)
            .ToListAsync();
    }

    public async Task<int> GetLeaveCountByEmployeeAndTypeAsync(string employeeId, LeaveType leaveType, int year, bool trackChanges)
    {
        return await FindByCondition(l => l.EmployeeId == employeeId && 
                                         l.LeaveType == leaveType && 
                                         l.StartDate.Year == year &&
                                         l.Status == LeaveStatus.Approved, trackChanges)
            .SumAsync(l => l.Days);
    }

    public void CreateLeave(Leave leave) => Create(leave);

    public void UpdateLeave(Leave leave) => Update(leave);

    public void DeleteLeave(Leave leave) => Delete(leave);

    public async Task<IEnumerable<Leave>> GetLeavesByTypeAsync(LeaveType type, LeaveRequestParameters parameters, bool trackChanges)
    {
        var query = FindByCondition(l => l.LeaveType == type, trackChanges)
            .Include(l => l.Employee)
            .Include(l => l.Approver)
            .Include(l => l.Rejector)
            .AsQueryable();

        // Apply filters
        query = ApplyFilters(query, parameters);

        // Apply sorting
        query = ApplySorting(query, parameters.SortBy, parameters.SortDirection);

        return await query.ToListAsync();
    }

    // Leave Balance operations
    public async Task<IEnumerable<LeaveBalance>> GetLeaveBalancesAsync(string? employeeId, int? year, bool trackChanges)
    {
        var query = RepositoryContext.Set<LeaveBalance>().AsQueryable();

        if (!string.IsNullOrEmpty(employeeId))
            query = query.Where(lb => lb.EmployeeId == employeeId);

        if (year.HasValue)
            query = query.Where(lb => lb.Year == year.Value);

        return await query
            .Include(lb => lb.Employee)
            .ToListAsync();
    }

    public async Task<LeaveBalance?> GetLeaveBalanceAsync(string employeeId, LeaveType leaveType, int year, bool trackChanges)
    {
        return await RepositoryContext.Set<LeaveBalance>()
            .Where(lb => lb.EmployeeId == employeeId && lb.LeaveType == leaveType && lb.Year == year)
            .Include(lb => lb.Employee)
            .FirstOrDefaultAsync();
    }

    public async Task<IEnumerable<LeaveBalance>> GetLeaveBalancesByEmployeeAsync(string employeeId, int year, bool trackChanges)
    {
        return await RepositoryContext.Set<LeaveBalance>()
            .Where(lb => lb.EmployeeId == employeeId && lb.Year == year)
            .Include(lb => lb.Employee)
            .ToListAsync();
    }

    public void CreateLeaveBalance(LeaveBalance leaveBalance) => RepositoryContext.Set<LeaveBalance>().Add(leaveBalance);

    public void UpdateLeaveBalance(LeaveBalance leaveBalance) => RepositoryContext.Set<LeaveBalance>().Update(leaveBalance);

    public void DeleteLeaveBalance(LeaveBalance leaveBalance) => RepositoryContext.Set<LeaveBalance>().Remove(leaveBalance);

    // Statistics and reporting
    public async Task<int> GetLeaveCountAsync() => await FindAll(false).CountAsync();

    public async Task<int> GetLeaveCountByStatusAsync(LeaveStatus status) =>
        await FindByCondition(l => l.Status == status, false).CountAsync();

    public async Task<Dictionary<LeaveStatus, int>> GetLeaveCountByStatusGroupAsync()
    {
        return await FindAll(false)
            .GroupBy(l => l.Status)
            .ToDictionaryAsync(g => g.Key, g => g.Count());
    }

    public async Task<int> GetLeaveCountByTypeAsync(LeaveType type) =>
        await FindByCondition(l => l.LeaveType == type, false).CountAsync();

    public async Task<Dictionary<LeaveType, int>> GetLeaveCountByTypeGroupAsync()
    {
        return await FindAll(false)
            .GroupBy(l => l.LeaveType)
            .ToDictionaryAsync(g => g.Key, g => g.Count());
    }

    public async Task<int> GetLeaveCountByEmployeeAsync(string employeeId) =>
        await FindByCondition(l => l.EmployeeId == employeeId, false).CountAsync();

    public async Task<int> GetPendingLeaveCountAsync() =>
        await FindByCondition(l => l.Status == LeaveStatus.Pending, false).CountAsync();

    public async Task<int> GetTotalLeaveDaysRequestedAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = FindAll(false);

        if (startDate.HasValue)
            query = query.Where(l => l.StartDate >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(l => l.EndDate <= endDate.Value);

        return await query.SumAsync(l => l.Days);
    }

    public async Task<int> GetTotalLeaveDaysApprovedAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = FindByCondition(l => l.Status == LeaveStatus.Approved, false);

        if (startDate.HasValue)
            query = query.Where(l => l.StartDate >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(l => l.EndDate <= endDate.Value);

        return await query.SumAsync(l => l.Days);
    }

    public async Task<LeaveType?> GetMostRequestedLeaveTypeAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = FindAll(false);

        if (startDate.HasValue)
            query = query.Where(l => l.StartDate >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(l => l.EndDate <= endDate.Value);

        var result = await query
            .GroupBy(l => l.LeaveType)
            .OrderByDescending(g => g.Count())
            .Select(g => g.Key)
            .FirstOrDefaultAsync();

        return result;
    }

    // Bulk operations
    public async Task<IEnumerable<Leave>> GetLeavesByIdsAsync(Guid[] ids, bool trackChanges)
    {
        return await FindByCondition(l => ids.Contains(l.Id), trackChanges)
            .Include(l => l.Employee)
            .Include(l => l.Approver)
            .Include(l => l.Rejector)
            .ToListAsync();
    }

    // Validation helpers
    public async Task<bool> HasOverlappingLeaveAsync(string employeeId, DateTime startDate, DateTime endDate, Guid? excludeLeaveId = null)
    {
        var query = FindByCondition(l => l.EmployeeId == employeeId &&
                                        l.Status != LeaveStatus.Rejected &&
                                        l.Status != LeaveStatus.Cancelled &&
                                        ((l.StartDate <= endDate && l.EndDate >= startDate)), false);

        if (excludeLeaveId.HasValue)
            query = query.Where(l => l.Id != excludeLeaveId.Value);

        return await query.AnyAsync();
    }

    public async Task<int> GetUsedLeaveDaysAsync(string employeeId, LeaveType leaveType, int year)
    {
        return await FindByCondition(l => l.EmployeeId == employeeId &&
                                         l.LeaveType == leaveType &&
                                         l.StartDate.Year == year &&
                                         l.Status == LeaveStatus.Approved, false)
            .SumAsync(l => l.Days);
    }

    private static IQueryable<Leave> ApplyFilters(IQueryable<Leave> query, LeaveRequestParameters parameters)
    {
        if (!string.IsNullOrEmpty(parameters.SearchTerm))
        {
            var searchTerm = parameters.SearchTerm.ToLower();
            query = query.Where(l => l.Employee.UserName!.ToLower().Contains(searchTerm) ||
                                    l.Employee.Email!.ToLower().Contains(searchTerm) ||
                                    l.Reason.ToLower().Contains(searchTerm));
        }

        if (parameters.Status.HasValue)
        {
            query = query.Where(l => l.Status == parameters.Status.Value);
        }

        if (parameters.Type.HasValue)
        {
            query = query.Where(l => l.LeaveType == parameters.Type.Value);
        }

        if (parameters.StartDate.HasValue)
        {
            query = query.Where(l => l.StartDate >= parameters.StartDate.Value);
        }

        if (parameters.EndDate.HasValue)
        {
            query = query.Where(l => l.EndDate <= parameters.EndDate.Value);
        }

        if (!string.IsNullOrEmpty(parameters.EmployeeId))
        {
            query = query.Where(l => l.EmployeeId == parameters.EmployeeId);
        }

        return query;
    }

    private IQueryable<Leave> ApplySorting(IQueryable<Leave> query, string? sortBy, string? sortDirection)
    {
        var isDescending = sortDirection?.ToLower() == "desc";

        return sortBy?.ToLower() switch
        {
            "submitteddate" => isDescending ? query.OrderByDescending(l => l.SubmittedDate) : query.OrderBy(l => l.SubmittedDate),
            "startdate" => isDescending ? query.OrderByDescending(l => l.StartDate) : query.OrderBy(l => l.StartDate),
            "enddate" => isDescending ? query.OrderByDescending(l => l.EndDate) : query.OrderBy(l => l.EndDate),
            "days" => isDescending ? query.OrderByDescending(l => l.Days) : query.OrderBy(l => l.Days),
            "status" => isDescending ? query.OrderByDescending(l => l.Status) : query.OrderBy(l => l.Status),
            "leavetype" => isDescending ? query.OrderByDescending(l => l.LeaveType) : query.OrderBy(l => l.LeaveType),
            "employeename" => isDescending ? query.OrderByDescending(l => l.Employee.UserName) : query.OrderBy(l => l.Employee.UserName),
            _ => query.OrderByDescending(l => l.SubmittedDate)
        };
    }
}