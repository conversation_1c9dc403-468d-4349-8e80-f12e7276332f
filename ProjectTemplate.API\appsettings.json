{"ConnectionStrings": {"sqlConnection": "server=localhost;database=BMP-LIGHT-DB;Trusted_Connection=true;TrustServerCertificate=true"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "JWT": {"ValidAudience": "https://localhost:5001;http://localhost:5000", "ValidIssuer": "https://localhost:5001;http://localhost:5000", "Secret": "YOUR_JWT_SECRET_KEY_HERE_MAKE_IT_LONG_AND_SECURE"}, "EmailConfiguration": {"From": "<EMAIL>", "SmtpServer": "smtp.gmail.com", "Port": 465, "Username": "<EMAIL>", "Password": "your-app-password"}, "AllowedHosts": "*"}