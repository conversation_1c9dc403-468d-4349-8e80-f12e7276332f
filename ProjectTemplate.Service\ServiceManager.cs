using AutoMapper;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using ProjectTemplate.Contracts;
using ProjectTemplate.Contracts.Repository;
using ProjectTemplate.Models.Entities;
using ProjectTemplate.Service.Contracts;

namespace ProjectTemplate.Service;

public sealed class ServiceManager : IServiceManager
{
    private readonly Lazy<IWorkflowService> _workflowService;
    private readonly Lazy<IRequestService> _requestService;
    private readonly Lazy<INotificationService> _notificationService;
    private readonly Lazy<IAuthenticationService> _authenticationService;
    private readonly Lazy<ILeaveService> _leaveService;
    private readonly Lazy<IPerformanceReviewService> _performanceReviewService;

    public ServiceManager(
        IRepositoryManager repositoryManager,
        ILoggerManager logger,
        IMapper mapper,
        UserManager<ApplicationUser> userManager,
        SignInManager<ApplicationUser> signInManager,
        RoleManager<IdentityRole> roleManager,
        IConfiguration configuration)
    {
        _workflowService = new Lazy<IWorkflowService>(() => new WorkflowService(repositoryManager, logger, mapper));
        _requestService = new Lazy<IRequestService>(() => new RequestService(repositoryManager, logger, mapper));
        _notificationService = new Lazy<INotificationService>(() => new NotificationService(repositoryManager, logger, mapper));
        _authenticationService = new Lazy<IAuthenticationService>(() => new AuthenticationService(userManager, signInManager, roleManager, configuration, logger, mapper));
        _leaveService = new Lazy<ILeaveService>(() => new LeaveService(repositoryManager, logger, mapper));
        _performanceReviewService = new Lazy<IPerformanceReviewService>(() => new PerformanceReviewService(repositoryManager, logger, mapper));
    }

    public IWorkflowService WorkflowService => _workflowService.Value;
    public IRequestService RequestService => _requestService.Value;
    public INotificationService NotificationService => _notificationService.Value;
    public IAuthenticationService AuthenticationService => _authenticationService.Value;
    public ILeaveService LeaveService => _leaveService.Value;
    public IPerformanceReviewService PerformanceReviewService => _performanceReviewService.Value;
}
