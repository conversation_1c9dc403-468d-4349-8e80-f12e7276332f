<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.17" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.17" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.17" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.17" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.17">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.17" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.17">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
    <PackageReference Include="NLog" Version="5.2.8" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.8" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ProjectTemplate.Contracts\ProjectTemplate.Contracts.csproj" />
    <ProjectReference Include="..\ProjectTemplate.LoggerService\ProjectTemplate.LoggerService.csproj" />
    <ProjectReference Include="..\ProjectTemplate.Models\ProjectTemplate.Models.csproj" />
    <ProjectReference Include="..\ProjectTemplate.Presentation\ProjectTemplate.Presentation.csproj" />
    <ProjectReference Include="..\ProjectTemplate.Repository\ProjectTemplate.Repository.csproj" />
    <ProjectReference Include="..\ProjectTemplate.Service.Contracts\ProjectTemplate.Service.Contracts.csproj" />
    <ProjectReference Include="..\ProjectTemplate.Service\ProjectTemplate.Service.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="wwwroot\**\*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

</Project>
